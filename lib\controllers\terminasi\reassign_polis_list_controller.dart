import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:pdl_superapp/models/termination_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/utils/keys.dart';

class ReassignPolisListController extends BaseControllers {
  final TextEditingController reasonTextController = TextEditingController();
  final TextEditingController polisTextController = TextEditingController();
  final RxBool hasChanges = false.obs;
  final RxString agentCode = ''.obs;
  final RxBool isSelfTermination = false.obs;
  late SharedPreferences prefs;

  RxList<TerminationModel> teamTerminasiList = <TerminationModel>[].obs;
  final Map<String, String> dropdownOptions = {};

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();
    agentCode.value = prefs.getString(kStorageAgentCode) ?? '';

    getTerminasiList();
    getAgentList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetTerminationList:
        // selfTerminasiList.clear();
        teamTerminasiList.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationModel.fromJson(datas[i]);
          if ((terminasiData.target?.agentCode) == agentCode.value) {
            // selfTerminasiList.add(terminasiData);
          } else {
            teamTerminasiList.add(terminasiData);
          }
        }
        break;
      case kReqGetEligibleCandidateTermination:
        dropdownOptions.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.fromJson(datas[i]);
          dropdownOptions[terminasiData.agentCode ?? ''] =
              terminasiData.agentName ?? '';
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  getTerminasiList() async {
    setLoading(true);
    await api.getTerminationList(
      controllers: this,
      params: null,
      code: kReqGetTerminationList,
    );
  }

  getAgentList() async {
    await api.getTerminationEligibleCandidates(
      controllers: this,
      query: null,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  void refreshData() {}
}
