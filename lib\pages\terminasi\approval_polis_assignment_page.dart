import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/controllers/terminasi/approval_polis_assignment_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ApprovalPolisAssignmentPage extends StatefulWidget {
  const ApprovalPolisAssignmentPage({super.key});

  @override
  State<ApprovalPolisAssignmentPage> createState() => _TerminasiPageState();
}

class _TerminasiPageState extends State<ApprovalPolisAssignmentPage> {
  final ApprovalPolisAssignmentController controller = Get.put(
    ApprovalPolisAssignmentController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BaseDetailPage(
          title: 'Persetujuan pengalihan polis',
          controller: controller,
          onRefresh: () async => controller.refreshData(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
            width: double.infinity,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: paddingLarge),
                  Text(
                    'title_termination'.tr,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 25),
                  _avatar(),
                  SizedBox(height: paddingMedium),
                  _infoKeagenan(context),
                  SizedBox(height: paddingLarge),
                  // controller.level.value == 'BM'
                  //     ?
                  // _pengalihanPolis(context),
                  // // : SizedBox.shrink(),
                  // const Divider(height: 1.0, color: kColorBorderLight),
                  // SizedBox(height: paddingMedium),
                  // _alasanTerminasi(context),
                  SizedBox(height: 129),
                ],
              ),
            ),
          ),
        ),
        Obx(
          () => Visibility(
            visible: controller.pendingStatus.isTrue,
            child: Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: _buttonAccept(context),
            ),
          ),
        ),
      ],
    );
  }

  Container _avatar() {
    return Container(
      width: double.infinity,
      height: 358,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey,
      ),
    );
  }

  Widget _buttonAccept(BuildContext context) {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(149, 157, 165, 0.2),
              blurRadius: 24,
              spreadRadius: 0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.all(paddingLarge),
        child: Row(
          children: [
            Expanded(
              child: PdlButton(
                controller: controller,
                onPressed: () {
                  PdlBaseDialog(
                    context: context,
                    child: PdlDialogContent(
                      message: 'title_reject_policy_transfer'.tr,
                      textButtonPos: 'button_reject'.tr,
                      onTap: () {
                        Get.back();
                        controller.rejectPolicyTransfer();
                      },
                    ),
                  );
                },
                backgroundColor: Colors.white,
                borderColor: Colors.red,
                foregorundColor: Colors.red,
                title: 'button_reject'.tr,
              ),
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: PdlButton(
                controller: controller,
                onPressed: () {
                  PdlBaseDialog(
                    context: context,
                    child: PdlDialogContent(
                      message: 'title_accept_policy_transfer'.tr,
                      textButtonPos: 'button_accept'.tr,
                      onTap: () {
                        Get.back();
                        controller.approvePolicyTransfer();
                      },
                    ),
                  );
                },
                title: 'button_accept'.tr,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _infoKeagenan(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'agent_to_be_terminated'.tr,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),

          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_submission_date'.tr,
            content: controller.submissionDate.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_current_level'.tr,
            content: controller.level.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_form_full_name'.tr,
            content: controller.agentName.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_form_agent_code'.tr,
            content: controller.agentCode.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_branch_name_code'.tr,
            content: controller.branchName.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_number_polis_transferred'.tr,
            content: controller.totalPolicies.value.toString(),
          ),
        ],
      );
    });
  }

  Widget _rowData(
    BuildContext context, {
    required String title,
    required String content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 150,
          child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
        ),
        Text(': ', style: Theme.of(context).textTheme.bodyMedium),
        SizedBox(width: paddingSmall),
        Expanded(
          flex: 3,
          child: Text(content, style: Theme.of(context).textTheme.bodyMedium),
        ),
      ],
    );
  }
}
