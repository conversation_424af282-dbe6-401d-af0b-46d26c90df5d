import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/terminasi/terminasi_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiPage extends StatefulWidget {
  const TerminasiPage({super.key});

  @override
  State<TerminasiPage> createState() => _TerminasiPageState();
}

class _TerminasiPageState extends State<TerminasiPage> {
  final TerminasiController controller = Get.put(
    TerminasiController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BaseDetailPage(
          title: 'title_termination'.tr,
          controller: controller,
          onRefresh: () async => controller.refreshData(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
            width: double.infinity,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: paddingLarge),
                  Text(
                    'title_termination'.tr,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 25),
                  _avatar(),
                  SizedBox(height: paddingMedium),
                  _infoKeagenan(context),
                  SizedBox(height: paddingLarge),
                  Obx(
                    () =>
                        controller.isSelfTermination.isFalse
                            ? _pengalihanPolis(context)
                            : SizedBox.shrink(),
                  ),
                  const Divider(height: 1.0, color: kColorBorderLight),
                  SizedBox(height: paddingMedium),
                  _alasanTerminasi(context),
                  SizedBox(height: 129),
                ],
              ),
            ),
          ),
        ),
        Positioned(bottom: 0, right: 0, left: 0, child: _buttonSubmit(context)),
      ],
    );
  }

  Column _pengalihanPolis(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'title_policy_transfer'.tr,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: paddingMedium),
        _TextfieldPengalihanPolis(controller: controller),
        SizedBox(height: paddingLarge),
      ],
    );
  }

  Container _avatar() {
    return Container(
      width: double.infinity,
      height: 358,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey,
      ),
    );
  }

  Widget _buttonSubmit(BuildContext context) {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(149, 157, 165, 0.2),
              blurRadius: 24,
              spreadRadius: 0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.all(paddingLarge),
        child: PdlButton(
          controller: controller,
          onPressed:
              controller.hasChanges.value
                  ? () => PdlBaseDialog(
                    context: context,
                    child: DialogTerminasiConfirmContent(
                      onTap: () {
                        Get.back();
                        controller.submitTerminasi();
                      },
                    ),
                  )
                  : null,
          title: 'button_submit_termination'.tr,
        ),
      );
    });
  }

  Column _alasanTerminasi(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Text(
                  'title_termination_reason'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Text(
                  ' (${'title_required_fields'.tr})',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Color(0xFF888888)),
                ),
              ),
            ],
          ),
        ),
        // Text(
        //   'Alasan Terminasi',
        //   style: Theme.of(
        //     context,
        //   ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        // ),
        SizedBox(height: paddingMedium),
        _textFieldAlasan(context),
      ],
    );
  }

  TextFormField _textFieldAlasan(BuildContext context) {
    return TextFormField(
      controller: controller.reasonTextController,
      style: Theme.of(context).textTheme.bodyMedium,
      maxLines: null,
      minLines: 3,
      maxLength: 200,
      decoration: InputDecoration(
        hintText: 'hint_termination_reason'.tr,
        hintStyle: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: kColorTextTersier),
        fillColor: Colors.transparent,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
          ),
        ),
      ),
    );
  }

  Widget _infoKeagenan(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'title_agency_information'.tr,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_form_agent_code'.tr,
            content: controller.agentCode.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_form_full_name'.tr,
            content: controller.agentName.value,
          ),
          SizedBox(height: paddingMedium),
          _rowData(
            context,
            title: 'label_current_level'.tr,
            content: controller.level.value,
          ),
        ],
      );
    });
  }

  Widget _rowData(
    BuildContext context, {
    required String title,
    required String content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 150,
          child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
        ),
        Text(': ', style: Theme.of(context).textTheme.bodyMedium),
        SizedBox(width: paddingSmall),
        Expanded(
          flex: 3,
          child: Text(content, style: Theme.of(context).textTheme.bodyMedium),
        ),
      ],
    );
  }
}

class _TextfieldPengalihanPolis extends StatelessWidget {
  const _TextfieldPengalihanPolis({required this.controller});
  final TerminasiController controller;

  @override
  Widget build(BuildContext context) {
    return RawAutocomplete<String>(
      textEditingController: controller.polisTextController,
      focusNode: FocusNode(),
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text == '') {
          return const Iterable<String>.empty();
        }
        return controller.dropdownOptions.entries
            .where((entry) {
              return entry.value.toLowerCase().contains(
                textEditingValue.text.toLowerCase(),
              );
            })
            .map((entry) => entry.key);
      },
      displayStringForOption: (String option) {
        return controller.dropdownOptions[option] ?? '';
      },
      onSelected: (String selection) {
        controller.selectedValue.value = selection;
        controller.polisTextController.text =
            controller.dropdownOptions[controller.selectedValue.value] ?? '';
      },
      fieldViewBuilder: (context, textController, focusNode, onFieldSubmitted) {
        return TextFormField(
          controller: textController,
          focusNode: focusNode,

          decoration: InputDecoration(
            fillColor: Colors.transparent,
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.all(paddingMedium),
              child: Utils.cachedSvgWrapper(
                'icon/ic-linear-search -2.svg',
                color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
              ),
            ),
            suffixIcon: InkWell(
              onTap: () {
                textController.text = '';
                focusNode.requestFocus();
              },
              child: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-x-close.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),

            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
              ),
            ),
          ),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Material(
          elevation: 4,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options.elementAt(index);
              final id = options.elementAt(index);
              final name = controller.dropdownOptions[id] ?? '';
              return ListTile(
                title: Text('($id) $name'),
                onTap: () => onSelected(option),
              );
            },
          ),
        );
      },
    );
  }
}

class DialogTerminasiConfirmContent extends StatelessWidget {
  final Function() onTap;
  final String? textButtonPos;
  final Widget? icon;

  const DialogTerminasiConfirmContent({
    super.key,
    required this.onTap,
    this.textButtonPos,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingSmall),
          SizedBox(
            width: 60,
            height: 50,
            child:
                icon ??
                Utils.cachedSvgWrapper('icon/ic-dialog-exclamation.svg'),
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: Text('label_terminasi_info'.tr, textAlign: TextAlign.center),
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(
                        Colors.transparent,
                      ),
                      side: WidgetStateProperty.all(
                        BorderSide(color: Color(0xFFD1D1D1)),
                      ),
                      foregroundColor: WidgetStateProperty.all(
                        Color(0xFF0C9DEB),
                      ),
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: paddingSmall),
                      ),
                    ),
                    child: Text(
                      'label_cancel'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    onPressed: () => Get.back(),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: FilledButton(
                    onPressed: onTap,
                    child: Text(textButtonPos ?? 'label_yes_sure'.tr),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
