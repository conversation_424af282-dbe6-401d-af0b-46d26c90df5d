import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:pdl_superapp/utils/keys.dart';

class TerminasiTeamController extends BaseControllers {
  RxList<TerminationCandidateModel> terminasiCandidateList =
      <TerminationCandidateModel>[].obs;
  final q = Rxn<String>();

  @override
  void onInit() {
    super.onInit();
    var agentLevel = Get.arguments[kArgsUserLevel] ?? kLevelBP;
    getTerminasiCandidateList(agentLevel);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetEligibleCandidateTermination:
        terminasiCandidateList.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.fromJson(datas[i]);
          terminasiCandidateList.add(terminasiData);
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  getTerminasiCandidateList(String agentLevel) async {
    String params = "BP";
    switch (agentLevel) {
      case kLevelBP:
        params = "BP";
        break;
      case kLevelBM:
        params = "BM";
        break;
      case kLevelBD:
        params = "BD";
        break;
      default:
        params = "BP";
        break;
    }

    Map<String, dynamic> queryParams = {'level': params};

    if (q.value != null && q.value!.trim().isNotEmpty) {
      queryParams['searchQuery'] = q.value;
    }

    setLoading(true);

    await api.getTerminationEligibleCandidates(
      controllers: this,
      query: queryParams,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  void refreshData() {}
}
