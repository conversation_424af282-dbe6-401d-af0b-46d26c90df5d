import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:pdl_superapp/models/termination_model.dart';
import 'package:pdl_superapp/utils/keys.dart';

class ReassignPolisPageController extends BaseControllers {
  final RxInt trxId = 0.obs;
  final RxString agentName = ''.obs;
  final RxString agentCode = ''.obs;
  final RxString level = ''.obs;

  final RxString assignedTo = ''.obs;
  final RxString assignedAgentCode = ''.obs;
  final RxString status = ''.obs;

  final TextEditingController polisTextController = TextEditingController();
  final Map<String, String> dropdownOptions = {};
  final RxString selectedValue = ''.obs;

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments[kArgsTerminationData] != null) {
      TerminationModel terminationData =
          Get.arguments[kArgsTerminationData] ?? '';

      getTerminationDetail(terminationData.id.toString());

      trxId.value = terminationData.id ?? 0;
    }
    getAgentList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetTerminationDetail:
        final terminationData = TerminationModel.fromJson(response);

        agentName.value = terminationData.target?.name ?? '';
        agentCode.value = terminationData.target?.agentCode ?? '';
        level.value = terminationData.target?.agentLevel ?? '';

        assignedTo.value =
            terminationData.policyTransferDto?.recipientName ?? '';
        assignedAgentCode.value =
            terminationData.policyTransferDto?.recipientAgentCode ?? '';
        status.value = terminationData.policyTransferDto?.status ?? '';
        break;
      case kReqGetEligibleCandidateTermination:
        dropdownOptions.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.fromJson(datas[i]);
          if (terminasiData.agentCode != assignedAgentCode.value) {
            dropdownOptions[terminasiData.agentCode ?? ''] =
                terminasiData.agentName ?? '';
          }
        }
        break;
      case kReqUpdateTargetPolicyTransfer:
        Get.back();
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  getTerminationDetail(String id) async {
    await api.getTerminationDetail(
      controllers: this,
      params: id,
      code: kReqGetTerminationDetail,
    );
  }

  getAgentList() async {
    await api.getTerminationEligibleCandidates(
      controllers: this,
      query: null,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  submitUpldatePolicyTransfer() async {
    Get.back();
    //changePolicyTransfer
    await api.changePolicyTransfer(
      controllers: this,
      terminationId: trxId.value,
      targetAgentCode: selectedValue.value,
      code: kReqUpdateTargetPolicyTransfer,
    );
  }

  void refreshData() {}
}
