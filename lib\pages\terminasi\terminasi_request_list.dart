import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/controllers/terminasi/terminasi_list_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiRequestList extends StatelessWidget {
  TerminasiRequestList({super.key});

  final TerminasiListController controller = Get.put(
    TerminasiListController(),
    tag: Utils.getRandomString(),
  );

  Widget _buildTab(BuildContext context, String text, int currentTab) {
    var isSelected = controller.activeTabIndex.value == currentTab;
    return Expanded(
      child: GestureDetector(
        onTap: () => controller.changeActiveTab(currentTab),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.surface
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(40),
            border: isSelected ? Border.all(color: Colors.grey) : null,
          ),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  isSelected
                      ? (Get.isDarkMode ? kColorTextDark : kColorTextLight)
                      : Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.getTerminasiList();
    return BaseDetailPage(
      title: 'title_termination_request_list'.tr,
      controller: controller,
      onRefresh: () async => controller.refreshData(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: paddingLarge),
            Obx(
              () => Visibility(
                visible: controller.showSelfTermination.isTrue,
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color:
                        Get.isDarkMode
                            ? kColorGlobalBgDarkBlue
                            : Colors.grey[200],
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildTab(context, "Terminasi Anda", 0),
                      SizedBox(width: 8),
                      _buildTab(context, "Terminasi Team", 1),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: paddingLarge),
            PdlTextField(
              hint: 'hint_search_agent_name_code'.tr,
              prefixIcon: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-search -2.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),
            SizedBox(height: paddingSmall),
            Obx(
              () => Visibility(
                visible: controller.activeTabIndex.value == 0,
                child: Column(
                  children: [
                    controller.isLoading.isTrue
                        ? const Center(child: CircularProgressIndicator())
                        : const SizedBox.shrink(),
                    (controller.selfTerminasiList.isEmpty &&
                            controller.isLoading.isFalse)
                        ? EmptyStateView(msg: 'Hmm.. belum ada terminasi')
                        : const SizedBox.shrink(),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: BouncingScrollPhysics(),
                      padding: EdgeInsets.only(top: paddingMedium, bottom: 18),
                      separatorBuilder:
                          (context, index) => Divider(color: kColorBorderLight),
                      itemCount: controller.selfTerminasiList.length,
                      itemBuilder:
                          (context, index) => InkWell(
                            onTap:
                                () => Get.toNamed(
                                  Routes.TERMINASI_DETAIL,
                                  arguments: {
                                    kArgsTerminationData:
                                        controller.selfTerminasiList[index],
                                    kArgsSelfTermination: true,
                                  },
                                ),
                            child: _AgentItem(
                              agentCode:
                                  controller
                                      .selfTerminasiList[index]
                                      .target
                                      ?.agentCode ??
                                  '',
                              agentLevel:
                                  controller
                                      .selfTerminasiList[index]
                                      .target
                                      ?.agentLevel ??
                                  '',
                              channel:
                                  controller
                                      .selfTerminasiList[index]
                                      .target
                                      ?.channel ??
                                  '',
                              agentName:
                                  controller
                                      .selfTerminasiList[index]
                                      .target
                                      ?.name ??
                                  '',
                              statusText:
                                  controller.selfTerminasiList[index].status ??
                                  '',
                            ),
                          ),
                    ),
                  ],
                ),
              ),
            ),
            Obx(
              () => Visibility(
                visible: controller.activeTabIndex.value == 1,
                child: Column(
                  children: [
                    controller.isLoading.isTrue
                        ? const Center(child: CircularProgressIndicator())
                        : const SizedBox.shrink(),
                    (controller.teamTerminasiList.isEmpty &&
                            controller.isLoading.isFalse)
                        ? EmptyStateView(msg: 'Hmm.. belum ada terminasi')
                        : const SizedBox.shrink(),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: BouncingScrollPhysics(),
                      padding: EdgeInsets.only(top: paddingMedium, bottom: 18),
                      separatorBuilder:
                          (context, index) => Divider(color: kColorBorderLight),
                      itemCount: controller.teamTerminasiList.length,
                      itemBuilder:
                          (context, index) => InkWell(
                            onTap:
                                () => Get.toNamed(
                                  Routes.TERMINASI_DETAIL,
                                  arguments: {
                                    kArgsTerminationData:
                                        controller.teamTerminasiList[index],
                                    kArgsSelfTermination: false,
                                  },
                                ),
                            child: _AgentItem(
                              agentCode:
                                  controller
                                      .teamTerminasiList[index]
                                      .target
                                      ?.agentCode ??
                                  '',
                              agentLevel:
                                  controller
                                      .teamTerminasiList[index]
                                      .target
                                      ?.agentLevel ??
                                  '',
                              channel:
                                  controller
                                      .teamTerminasiList[index]
                                      .target
                                      ?.channel ??
                                  '',
                              agentName:
                                  controller
                                      .teamTerminasiList[index]
                                      .target
                                      ?.name ??
                                  '',
                              statusText:
                                  controller.teamTerminasiList[index].status ??
                                  '',
                            ),
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AgentItem extends StatelessWidget {
  final String agentCode;
  final String agentName;
  final String channel;
  final String agentLevel;
  final String statusText;
  const _AgentItem({
    required this.agentCode,
    required this.agentName,
    required this.channel,
    required this.agentLevel,
    required this.statusText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(width: 44, height: 44, child: CircleAvatar()),
        SizedBox(width: paddingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Terminasi',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorTextTersierLight,
                ),
              ),
              SizedBox(height: paddingSmall),
              Text(
                agentName,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: paddingSmall),
              Text(
                '$agentLevel $channel - $agentCode',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorTextTersierLight,
                ),
              ),
              SizedBox(height: paddingSmall),
              Container(
                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  color: Color(0xFFF0F8FF),
                ),
                child: Text(
                  statusText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: kColorGlobalBlue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
