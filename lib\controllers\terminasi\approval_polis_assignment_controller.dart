import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/terminasi/approval_polis_assignment_list_controller.dart';
import 'package:pdl_superapp/models/policy_transfer_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApprovalPolisAssignmentController extends BaseControllers {
  final RxString submissionDate = ''.obs;
  final RxString level = ''.obs;
  final RxBool pendingStatus = false.obs;
  final RxString agentName = ''.obs;
  final RxString agentCode = ''.obs;
  final RxString branchCode = ''.obs;
  final RxString branchName = ''.obs;
  final RxInt totalPolicies = 0.obs;
  final RxInt terminationId = 0.obs;

  late SharedPreferences prefs;

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();

    if (Get.arguments[kArgsTerminationData] != null) {
      PolicyTransferModel polisTransferData =
          Get.arguments[kArgsTerminationData] ?? '';

      submissionDate.value = DateFormat(
        'dd MMMM yyyy',
        'id_ID',
      ).format(polisTransferData.requestedAt!);
      agentName.value = polisTransferData.sourceAgentName ?? '';
      agentCode.value = polisTransferData.sourceAgentCode ?? '';
      level.value = polisTransferData.level ?? '';
      totalPolicies.value = polisTransferData.totalPolicies ?? 0;
      branchCode.value = polisTransferData.branchCode ?? '';
      branchName.value = polisTransferData.branchName ?? '';
      terminationId.value = polisTransferData.trxTerminationId ?? 0;

      pendingStatus.value =
          polisTransferData.status == 'PENDING' ||
          polisTransferData.status == 'NEED_APPROVAL';
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    final ApprovalPolisAssignmentListController listController =
        Get.find<ApprovalPolisAssignmentListController>();

    switch (requestCode) {
      case kReqAcceptPolicyTransfer:
        listController.getPolicyTransferList();
        Get.back();
        break;
      case kReqRejectPolicyTransfer:
        listController.getPolicyTransferList();
        Get.back();
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  approvePolicyTransfer() async {
    setLoading(true);
    await api.approvePolicyTransfer(
      controllers: this,
      terminationId: terminationId.value,
      code: kReqAcceptPolicyTransfer,
    );
  }

  rejectPolicyTransfer() async {
    setLoading(true);
    await api.rejectPolicyTransfer(
      controllers: this,
      terminationId: terminationId.value,
      code: kReqRejectPolicyTransfer,
    );
  }

  void refreshData() {}
}
