class ApprovalTerminasiModel {
  ApprovalTerminasiModel({
    required this.id,
    required this.reason,
    required this.status,
    required this.approvalStatus,
    required this.requestedBy,
    required this.target,
    required this.createdAt,
    required this.updatedAt,
    required this.approvalDetails,
    required this.policyTransferInfo,
    required this.policyTransferDto,
  });

  final int? id;
  final String? reason;
  final String? status;
  final String? approvalStatus;
  final RequestedBy? requestedBy;
  final RequestedBy? target;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<dynamic> approvalDetails;
  final String? policyTransferInfo;
  final PolicyTransferDto? policyTransferDto;

  factory ApprovalTerminasiModel.fromJson(Map<String, dynamic> json) {
    return ApprovalTerminasiModel(
      id: json["id"],
      reason: json["reason"],
      status: json["status"],
      approvalStatus: json["approvalStatus"],
      requestedBy:
          json["requestedBy"] == null
              ? null
              : RequestedBy.fromJson(json["requestedBy"]),
      target:
          json["target"] == null ? null : RequestedBy.fromJson(json["target"]),
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      approvalDetails:
          json["approvalDetails"] == null
              ? []
              : List<dynamic>.from(json["approvalDetails"]!.map((x) => x)),
      policyTransferInfo: "",
      policyTransferDto:
          json["policyTransferDto"] == null
              ? null
              : PolicyTransferDto.fromJson(json["policyTransferDto"]),
    );
  }
}

class RequestedBy {
  RequestedBy({
    required this.username,
    required this.name,
    required this.channel,
    required this.agentCode,
    required this.agentLevel,
    required this.picture,
    required this.roles,
  });

  final String? username;
  final String? name;
  final String? channel;
  final String? agentCode;
  final String? agentLevel;
  final String? picture;
  final List<Role> roles;

  factory RequestedBy.fromJson(Map<String, dynamic> json) {
    return RequestedBy(
      username: json["username"],
      name: json["name"],
      channel: json["channel"],
      agentCode: json["agentCode"],
      agentLevel: json["agentLevel"],
      picture: json["picture"],
      roles:
          json["roles"] == null
              ? []
              : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
    );
  }
}

class Role {
  Role({
    required this.code,
    required this.name,
    required this.channel,
    required this.platform,
  });

  final String? code;
  final String? name;
  final String? channel;
  final String? platform;

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      code: json["code"],
      name: json["name"],
      channel: json["channel"],
      platform: json["platform"],
    );
  }
}

class PolicyTransferDto {
  PolicyTransferDto({
    required this.recipientName,
    required this.recipientAgentCode,
    required this.recipientPicture,
    required this.level,
    required this.sourceAgentCode,
    required this.sourceAgentName,
    required this.sourceAgentPicture,
    required this.sourceAgentLevel,
    required this.branchCode,
    required this.branchName,
    required this.totalPolicies,
    required this.submittedBy,
    required this.submitterAgentCode,
    required this.status,
    required this.requestedAt,
    required this.trxTerminationId,
  });

  final String? recipientName;
  final String? recipientAgentCode;
  final String? recipientPicture;
  final String? level;
  final String? sourceAgentCode;
  final String? sourceAgentName;
  final String? sourceAgentPicture;
  final String? sourceAgentLevel;
  final String? branchCode;
  final String? branchName;
  final int? totalPolicies;
  final String? submittedBy;
  final String? submitterAgentCode;
  final String? status;
  final DateTime? requestedAt;
  final int? trxTerminationId;

  factory PolicyTransferDto.fromJson(Map<String, dynamic> json) {
    return PolicyTransferDto(
      recipientName: json["recipientName"],
      recipientAgentCode: json["recipientAgentCode"],
      recipientPicture: json["recipientPicture"],
      level: json["level"],
      sourceAgentCode: json["sourceAgentCode"],
      sourceAgentName: json["sourceAgentName"],
      sourceAgentPicture: json["sourceAgentPicture"],
      sourceAgentLevel: json["sourceAgentLevel"],
      branchCode: json["branchCode"],
      branchName: json["branchName"],
      totalPolicies: json["totalPolicies"],
      submittedBy: json["submittedBy"],
      submitterAgentCode: json["submitterAgentCode"],
      status: json["status"],
      requestedAt: DateTime.tryParse(json["requestedAt"] ?? ""),
      trxTerminationId: json["trxTerminationId"],
    );
  }
}
